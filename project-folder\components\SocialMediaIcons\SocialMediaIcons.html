<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social Media Icons Carousel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Custom styles for exact match with React version */
        .social-media-section {
            width: 100%;
            padding: 9rem 1rem;
            background-color: #FFFFFF;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
        }

        .content-wrapper {
            margin: 0 auto;
            text-align: center;
            position: relative;
            z-index: 10;
            width: 100%;
            margin-top: -1.25rem;
        }

        .section-title {
            font-size: 1.875rem;
            font-weight: 500;
            color: #563D39;
            margin-bottom: 1.5rem;
            line-height: 1.25;
        }

        .section-subtitle {
            min-height: 10vh;
            font-size: 1rem;
            color: rgba(0, 0, 0, 0.6);
            max-width: 64rem;
            margin-left: auto;
            margin-right: auto;
            font-weight: 300;
            margin-bottom: 4.875rem;
        }

        @media (min-width: 768px) {
            .section-title {
                font-size: 2.375rem;
            }
            .section-subtitle {
                font-size: 1.25rem;
            }
        }

        @media (min-width: 992px) {
            .section-title {
                font-size: 2.6875rem;
            }
            .section-subtitle {
                font-size: 1.5rem;
            }
        }

        .icons-container {
            position: relative;
        }

        .background-images {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
        }

        .background-images img {
            position: absolute;
            width: 583px;
            height: 500px;
        }

        .carousel-container {
            position: relative;
            overflow: hidden;
            height: 150px;
        }

        @media (max-width: 640px) {
            .carousel-container {
                height: 120px;
            }
        }

        .icons-track {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: opacity 1s ease;
        }

        .icons-track.restarting {
            opacity: 0.3;
        }

        .social-icon {
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 9px;
            transition: all 1s ease-in-out;
        }

        .icon-container {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 9px;
            transition: all 1s ease-in-out;
            flex-shrink: 0;
            background-color: #f8f9fa;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            width: 62px;
            height: 62px;
            min-width: 62px;
            min-height: 62px;
            max-width: 62px;
            max-height: 62px;
        }

        .icon-container.highlighted {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .social-icon-img {
            object-fit: contain;
            position: relative;
            z-index: 10;
            transition: all 1s ease;
            flex-shrink: 0;
            width: 40px;
            height: 40px;
            min-width: 40px;
            min-height: 40px;
            max-width: 40px;
            max-height: 40px;
        }

        .icon-bracket {
            position: absolute;
            width: 1.5rem;
            height: 1.5rem;
            border-width: 2px;
            border-style: solid;
            border-color: #d1d5db;
            z-index: 20;
            transition: border-color 1s ease;
        }

        .bracket-tl {
            top: -0.75rem;
            left: -0.75rem;
            border-top: 2px solid;
            border-left: 2px solid;
            border-radius: 10px 0 0 0;
        }

        .bracket-tr {
            top: -0.75rem;
            right: -0.75rem;
            border-top: 2px solid;
            border-right: 2px solid;
            border-radius: 0 10px 0 0;
        }

        .bracket-bl {
            bottom: -0.75rem;
            left: -0.75rem;
            border-bottom: 2px solid;
            border-left: 2px solid;
            border-radius: 0 0 0 10px;
        }

        .bracket-br {
            bottom: -0.75rem;
            right: -0.75rem;
            border-bottom: 2px solid;
            border-right: 2px solid;
            border-radius: 0 0 10px 0;
        }

        @media (max-width: 640px) {
            .icon-container {
                width: 56px;
                height: 56px;
                min-width: 56px;
                min-height: 56px;
                max-width: 56px;
                max-height: 56px;
            }
            
            .icon-container.highlighted {
                width: 72px;
                height: 72px;
                min-width: 72px;
                min-height: 72px;
                max-width: 72px;
                max-height: 72px;
            }
            
            .social-icon-img {
                width: 32px;
                height: 32px;
                min-width: 32px;
                min-height: 32px;
                max-width: 32px;
                max-height: 32px;
            }
            
            .social-icon-img.highlighted {
                width: 48px;
                height: 48px;
                min-width: 48px;
                min-height: 48px;
                max-width: 48px;
                max-height: 48px;
            }
            
            .bracket-tl,
            .bracket-bl {
                left: -1.5rem;
            }
            
            .bracket-tr,
            .bracket-br {
                right: -1.5rem;
            }
        }
    </style>
</head>
<body>
    <section class="social-media-section">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-12">
                    <div class="content-wrapper">
                        <!-- Title -->
                        <h2 class="section-title">
                            Streamline Your Social Media<br>
                            With Flowkar
                        </h2>
                        
                        <!-- Subtitle -->
                        <p class="section-subtitle">
                            One tool to schedule, track, and grow your socials.
                        </p>
                        
                        <!-- Social Media Icons Container -->
                        <div class="icons-container">
                            <!-- Background Images -->
                            <div class="background-images">
                                <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Background_Shadow-shUizQnGcNhErNuK4Cd5UTBqjG9ZzP.svg" alt="Shadow" class="bg-shadow" loading="lazy">
                                <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Background_03-hxju9NR17R6bvWAmAYgIpBAqBcFffx.svg" alt="" class="bg-gradient" loading="lazy">
                            </div>
                            
                            <!-- Icons Carousel -->
                            <div class="carousel-container">
                                <div class="icons-track" id="iconsTrack"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        class SocialMediaCarousel {
            constructor() {
                this.animationOffset = 0;
                this.roundCount = 0;
                this.isRestarting = false;
                this.spacing = 130;
                this.isMobile = false;
                this.intervalId = null;
                
                // Social media icons data
                this.baseSocialIcons = [
                    { 
                        platform: 'youtube', 
                        alt: 'YouTube', 
                        color: '#FF0000', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/youtube-icon-9Qq7J1lQYJX2kL3r7V5cB8Qe7vKQ4.svg' 
                    },
                    { 
                        platform: 'reddit', 
                        alt: 'Reddit', 
                        color: '#FF4500', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/reddit-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'instagram', 
                        alt: 'Instagram', 
                        color: '#E4405F', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/instagram-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'facebook', 
                        alt: 'Facebook', 
                        color: '#1877F2', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/facebook-icon-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'twitter', 
                        alt: 'X (Twitter)', 
                        color: '#000000', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/twitter-icon-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'pinterest', 
                        alt: 'Pinterest', 
                        color: '#BD081C', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/pintrest-icon-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'linkedin', 
                        alt: 'LinkedIn', 
                        color: '#0A66C2', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/linkdin-icon-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'threads', 
                        alt: 'Threads', 
                        color: '#000000', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/thread-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'reddit', 
                        alt: 'Reddit', 
                        color: '#FF4500', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/reddit-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'youtube', 
                        alt: 'YouTube', 
                        color: '#FF0000', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/youtube-icon-9Qq7J1lQYJX2kL3r7V5cB8Qe7vKQ4.svg' 
                    },
                    { 
                        platform: 'facebook', 
                        alt: 'Facebook', 
                        color: '#1877F2', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/facebook-icon-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'instagram', 
                        alt: 'Instagram', 
                        color: '#E4405F', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/instagram-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'threads', 
                        alt: 'Threads', 
                        color: '#000000', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/thread-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'pinterest', 
                        alt: 'Pinterest', 
                        color: '#BD081C', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/pintrest-icon-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'twitter', 
                        alt: 'X (Twitter)', 
                        color: '#000000', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/twitter-icon-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'linkedin', 
                        alt: 'LinkedIn', 
                        color: '#0A66C2', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/linkdin-icon-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'instagram', 
                        alt: 'Instagram', 
                        color: '#E4405F', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/instagram-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'reddit', 
                        alt: 'Reddit', 
                        color: '#FF4500', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/reddit-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'pinterest', 
                        alt: 'Pinterest', 
                        color: '#BD081C', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/pintrest-icon-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'youtube', 
                        alt: 'YouTube', 
                        color: '#FF0000', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/youtube-icon-9Qq7J1lQYJX2kL3r7V5cB8Qe7vKQ4.svg' 
                    },
                    { 
                        platform: 'linkedin', 
                        alt: 'LinkedIn', 
                        color: '#0A66C2', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/linkdin-icon-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'twitter', 
                        alt: 'X (Twitter)', 
                        color: '#000000', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/twitter-icon-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'facebook', 
                        alt: 'Facebook', 
                        color: '#1877F2', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/facebook-icon-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    },
                    { 
                        platform: 'threads', 
                        alt: 'Threads', 
                        color: '#000000', 
                        icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/thread-1XQZ5e7s3WQJfQm5w8k9q1v5q7J1lQ.svg' 
                    }
                ];
                
                this.socialIcons = this.generateSocialIcons(15);
                this.centerPosition = Math.floor(this.socialIcons.length / 2);
                
                this.init();
            }
            
            generateSocialIcons(repeatCount) {
                return Array(repeatCount).fill(null).flatMap(() => [...this.baseSocialIcons]);
            }
            
            init() {
                this.handleResize();
                this.createIcons();
                this.startAnimation();
                
                window.addEventListener('resize', () => this.handleResize());
            }
            
            handleResize() {
                const mobile = window.innerWidth <= 640;
                const newSpacing = mobile ? 170 : window.innerWidth <= 768 ? 100 : 130;
                this.spacing = newSpacing;
                this.isMobile = mobile;
                
                if (this.iconsCreated) {
                    this.updateIconPositions();
                }
            }
            
            createIcons() {
                const track = document.getElementById('iconsTrack');
                track.innerHTML = '';
                
                this.socialIcons.forEach((social, index) => {
                    const iconElement = this.createIconElement(social, index);
                    track.appendChild(iconElement);
                });
                
                this.iconsCreated = true;
                this.updateIconPositions();
            }
            
            createIconElement(social, index) {
                const iconDiv = document.createElement('div');
                iconDiv.className = 'social-icon';
                iconDiv.dataset.index = index;
                
                // Create corner brackets
                const brackets = [
                    { position: 'tl', styles: 'top: -0.75rem; left: -0.75rem; border-top: 2px solid; border-left: 2px solid; border-radius: 10px 0 0 0;' },
                    { position: 'tr', styles: 'top: -0.75rem; right: -0.75rem; border-top: 2px solid; border-right: 2px solid; border-radius: 0 10px 0 0;' },
                    { position: 'bl', styles: 'bottom: -0.75rem; left: -0.75rem; border-bottom: 2px solid; border-left: 2px solid; border-radius: 0 0 0 10px;' },
                    { position: 'br', styles: 'bottom: -0.75rem; right: -0.75rem; border-bottom: 2px solid; border-right: 2px solid; border-radius: 0 0 10px 0;' }
                ].map(bracket => {
                    const element = document.createElement('span');
                    element.className = `icon-bracket bracket-${bracket.position}`;
                    element.style.cssText = bracket.styles;
                    return element;
                });
                
                // Create icon container
                const container = document.createElement('div');
                container.className = 'icon-container';
                
                // Create actual image icon
                const icon = document.createElement('img');
                icon.className = 'social-icon-img';
                icon.src = social.icon;
                icon.alt = social.alt;
                icon.loading = 'lazy';
                icon.decoding = 'async';
                
                // Handle image load error - fallback to platform name
                icon.onerror = function() {
                    const fallback = document.createElement('div');
                    fallback.className = 'social-icon-fallback';
                    fallback.textContent = social.platform.charAt(0).toUpperCase();
                    fallback.style.cssText = `
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: bold;
                        color: ${social.color};
                        background: ${social.color}20;
                        border-radius: 4px;
                        width: 100%;
                        height: 100%;
                    `;
                    this.parentNode.replaceChild(fallback, this);
                };
                
                // Assemble the structure
                brackets.forEach(bracket => iconDiv.appendChild(bracket));
                container.appendChild(icon);
                iconDiv.appendChild(container);
                
                return iconDiv;
            }
            
            getIconPosition(index) {
                const basePosition = (index - this.centerPosition) * this.spacing;
                return basePosition - this.animationOffset;
            }
            
            getIconScale(index) {
                const effectiveCenterIndex = this.centerPosition + this.animationOffset / this.spacing;
                const cycleLength = this.baseSocialIcons.length;
                const iconCyclePosition = index % cycleLength;
                const centerCyclePosition = effectiveCenterIndex % cycleLength;
                
                let distance = Math.abs(iconCyclePosition - centerCyclePosition);
                distance = Math.min(distance, cycleLength - distance);
                
                if (distance < 0.5) return 1;
                if (distance < 1.5) return 0.8;
                if (distance < 2.5) return 0.7;
                return 0.6;
            }
            
            getIconOpacity(index) {
                const effectiveCenterIndex = this.centerPosition + this.animationOffset / this.spacing;
                const cycleLength = this.baseSocialIcons.length;
                const iconCyclePosition = index % cycleLength;
                const centerCyclePosition = effectiveCenterIndex % cycleLength;
                
                let distance = Math.abs(iconCyclePosition - centerCyclePosition);
                distance = Math.min(distance, cycleLength - distance);
                
                if (distance < 0.5) return 1;
                if (distance <= 2.5) return 0.8;
                if (distance <= 3.5) return 0.8;
                return 0.4;
            }
            
            isIconHighlighted(index) {
                const effectiveCenterIndex = this.centerPosition + this.animationOffset / this.spacing;
                const cycleLength = this.baseSocialIcons.length;
                const iconCyclePosition = index % cycleLength;
                const centerCyclePosition = effectiveCenterIndex % cycleLength;
                
                let distance = Math.abs(iconCyclePosition - centerCyclePosition);
                distance = Math.min(distance, cycleLength - distance);
                
                return distance < 0.5;
            }
            
            updateIconPositions() {
                const track = document.getElementById('iconsTrack');
                const icons = track.querySelectorAll('.social-icon');
                
                icons.forEach((iconElement, index) => {
                    const social = this.socialIcons[index];
                    const scale = this.getIconScale(index);
                    const opacity = this.getIconOpacity(index);
                    const translateX = this.getIconPosition(index);
                    const isHighlighted = this.isIconHighlighted(index);
                    
                    // Update transform and opacity
                    const transform = this.isMobile 
                        ? `translateX(${translateX}px)` 
                        : `translateX(${translateX}px) scale(${scale})`;
                    
                    iconElement.style.transform = transform;
                    iconElement.style.opacity = opacity;
                    iconElement.style.zIndex = isHighlighted ? 20 : 10 - Math.floor(distance);
                    
                    // Update brackets color
                    const brackets = iconElement.querySelectorAll('.icon-bracket');
                    brackets.forEach(bracket => {
                        bracket.style.borderColor = isHighlighted ? social.color : '#d1d5db';
                    });
                    
                    // Update container
                    const container = iconElement.querySelector('.icon-container');
                    const iconImg = iconElement.querySelector('.social-icon-img, .social-icon-fallback');
                    
                    if (isHighlighted) {
                        container.classList.add('highlighted');
                        container.style.backgroundColor = `${social.color}10`;
                        
                        // Set highlighted sizes
                        const containerSize = this.isMobile ? '72px' : '96px';
                        const iconSize = this.isMobile ? '48px' : '64px';
                        
                        container.style.width = containerSize;
                        container.style.height = containerSize;
                        container.style.minWidth = containerSize;
                        container.style.minHeight = containerSize;
                        
                        if (iconImg.tagName === 'IMG') {
                            iconImg.style.width = iconSize;
                            iconImg.style.height = iconSize;
                            iconImg.style.minWidth = iconSize;
                            iconImg.style.minHeight = iconSize;
                            iconImg.style.maxWidth = iconSize;
                            iconImg.style.maxHeight = iconSize;
                        } else {
                            iconImg.style.fontSize = `${parseInt(iconSize) * 0.6}px`;
                        }
                    } else {
                        container.classList.remove('highlighted');
                        container.style.backgroundColor = '#f8f9fa';
                        
                        // Set normal sizes
                        const containerSize = this.isMobile ? '56px' : '62px';
                        const iconSize = this.isMobile ? '32px' : '40px';
                        
                        container.style.width = containerSize;
                        container.style.height = containerSize;
                        container.style.minWidth = containerSize;
                        container.style.minHeight = containerSize;
                        
                        if (iconImg.tagName === 'IMG') {
                            iconImg.style.width = iconSize;
                            iconImg.style.height = iconSize;
                            iconImg.style.minWidth = iconSize;
                            iconImg.style.minHeight = iconSize;
                            iconImg.style.maxWidth = iconSize;
                            iconImg.style.maxHeight = iconSize;
                        } else {
                            iconImg.style.fontSize = `${parseInt(iconSize) * 0.6}px`;
                        }
                    }
                });
            }
            
            startAnimation() {
                this.intervalId = setInterval(() => {
                    if (this.isRestarting) return;
                    
                    this.animationOffset += this.spacing;
                    
                    const cycleLength = this.baseSocialIcons.length * this.spacing;
                    
                    if (this.animationOffset >= cycleLength) {
                        this.roundCount++;
                        
                        if (this.roundCount >= 15) {
                            this.isRestarting = true;
                            const track = document.getElementById('iconsTrack');
                            track.classList.add('restarting');
                            
                            setTimeout(() => {
                                this.animationOffset = 0;
                                this.roundCount = 0;
                                this.isRestarting = false;
                                track.classList.remove('restarting');
                                this.updateIconPositions();
                            }, 1000);
                            
                            return;
                        }
                        
                        this.animationOffset = 0;
                    }
                    
                    this.updateIconPositions();
                }, 2000);
            }
            
            destroy() {
                if (this.intervalId) {
                    clearInterval(this.intervalId);
                }
                window.removeEventListener('resize', this.handleResize);
            }
        }

        // Initialize the carousel when the DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new SocialMediaCarousel();
        });
    </script>
</body>
</html>